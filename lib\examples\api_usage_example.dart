import '../services/user_service.dart';
import '../models/user.dart';

/// Example showing how to use the UserService
/// This demonstrates all CRUD operations
class ApiUsageExample {
  final UserService _userService = UserService();

  /// Example of how to use all the UserService methods
  Future<void> demonstrateApiUsage() async {
    print('🚀 Starting API Usage Demonstration\n');

    try {
      // 1. Get all users
      print('📋 1. Fetching all users...');
      final allUsers = await _userService.getAllUsers();
      print('✅ Found ${allUsers.length} users');
      print('First user: ${allUsers.isNotEmpty ? allUsers.first.name : 'None'}\n');

      // 2. Get a specific user
      if (allUsers.isNotEmpty) {
        final firstUserId = allUsers.first.id!;
        print('👤 2. Fetching user with ID $firstUserId...');
        final user = await _userService.getUserById(firstUserId);
        print('✅ Found user: ${user.name} (${user.email})\n');
      }

      // 3. Create a new user
      print('➕ 3. Creating a new user...');
      final newUserRequest = CreateUserRequest(
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        website: 'johndoe.com',
      );
      
      final createdUser = await _userService.createUser(newUserRequest);
      print('✅ Created user: ${createdUser.name} with ID ${createdUser.id}\n');

      // 4. Update the user
      if (createdUser.id != null) {
        print('✏️ 4. Updating user ${createdUser.id}...');
        final updateRequest = CreateUserRequest(
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '******-0456',
        );
        
        final updatedUser = await _userService.updateUser(createdUser.id!, updateRequest);
        print('✅ Updated user: ${updatedUser.name}\n');

        // 5. Partially update the user
        print('🔧 5. Partially updating user ${createdUser.id}...');
        final patchedUser = await _userService.patchUser(createdUser.id!, {
          'website': 'johnsmith.dev',
        });
        print('✅ Patched user website\n');

        // 6. Delete the user
        print('🗑️ 6. Deleting user ${createdUser.id}...');
        final deleted = await _userService.deleteUser(createdUser.id!);
        print('✅ User deleted: $deleted\n');
      }

      // 7. Search users
      print('🔍 7. Searching for users with "Bret"...');
      final searchResults = await _userService.searchUsers('Bret');
      print('✅ Found ${searchResults.length} users matching "Bret"');
      for (final user in searchResults) {
        print('   - ${user.name} (${user.email})');
      }
      print('');

      // 8. Get paginated users
      print('📄 8. Getting paginated users (page 1, limit 3)...');
      final paginatedResponse = await _userService.getUsersPaginated(
        page: 1,
        limit: 3,
      );
      print('✅ Page ${paginatedResponse.page} of ${paginatedResponse.totalPages}');
      print('   Users on this page: ${paginatedResponse.users.length}');
      print('   Total users: ${paginatedResponse.totalCount}');
      print('   Has next page: ${paginatedResponse.hasNextPage}');
      print('   Has previous page: ${paginatedResponse.hasPreviousPage}\n');

      print('🎉 API Usage Demonstration completed successfully!');

    } catch (e) {
      print('❌ Error during demonstration: $e');
    }
  }

  /// Example of error handling
  Future<void> demonstrateErrorHandling() async {
    print('\n🚨 Demonstrating Error Handling\n');

    try {
      // Try to get a user that doesn't exist
      print('Trying to fetch user with ID 999...');
      await _userService.getUserById(999);
    } catch (e) {
      print('✅ Caught expected error: $e\n');
    }

    try {
      // Try to create a user with invalid data
      print('Trying to create user with invalid email...');
      final invalidRequest = CreateUserRequest(
        name: '',
        email: 'invalid-email',
      );
      await _userService.createUser(invalidRequest);
    } catch (e) {
      print('✅ Caught validation error: $e\n');
    }

    print('🎯 Error handling demonstration completed!');
  }

  /// Example of concurrent requests
  Future<void> demonstrateConcurrentRequests() async {
    print('\n⚡ Demonstrating Concurrent Requests\n');

    try {
      final stopwatch = Stopwatch()..start();

      // Make multiple requests concurrently
      final futures = [
        _userService.getUserById(1),
        _userService.getUserById(2),
        _userService.getUserById(3),
        _userService.getAllUsers(),
      ];

      final results = await Future.wait(futures);
      stopwatch.stop();

      print('✅ Completed ${futures.length} concurrent requests');
      print('⏱️ Total time: ${stopwatch.elapsedMilliseconds}ms');
      print('📊 Results:');
      print('   - User 1: ${(results[0] as User).name}');
      print('   - User 2: ${(results[1] as User).name}');
      print('   - User 3: ${(results[2] as User).name}');
      print('   - All users count: ${(results[3] as List<User>).length}');

    } catch (e) {
      print('❌ Error in concurrent requests: $e');
    }
  }
}

/// Function to run all examples
Future<void> runAllExamples() async {
  final example = ApiUsageExample();
  
  await example.demonstrateApiUsage();
  await example.demonstrateErrorHandling();
  await example.demonstrateConcurrentRequests();
  
  print('\n🏁 All examples completed!');
}
