// Exemple d'utilisation simple de l'ApiClient
import 'api_client.dart';

// Fonction d'exemple pour montrer comment utiliser l'ApiClient
void exempleUtilisation() async {
  // 1. Créer une instance de l'ApiClient
  final apiClient = ApiClient();
  
  try {
    // 2. Récupérer des données (GET)
    print('=== Exemple 1: Récupérer des utilisateurs ===');
    final utilisateurs = await apiClient.getData('/users');
    print('Utilisateurs reçus: $utilisateurs');
    
    // 3. Envoyer des nouvelles données (POST)
    print('\n=== Exemple 2: Créer un nouvel utilisateur ===');
    final nouvelUtilisateur = {
      'nom': 'Marie',
      'email': '<EMAIL>',
      'age': 25
    };
    final utilisateurCree = await apiClient.sendData('/users', nouvelUtilisateur);
    print('Utilisateur créé: $utilisateurCree');
    
    // 4. Mettre à jour des données (PUT)
    print('\n=== Exemple 3: Modifier un utilisateur ===');
    final donneesModifiees = {
      'nom': 'Marie Dupont',
      'email': '<EMAIL>',
      'age': 26
    };
    final utilisateurModifie = await apiClient.updateData('/users/1', donneesModifiees);
    print('Utilisateur modifié: $utilisateurModifie');
    
    // 5. Supprimer des données (DELETE)
    print('\n=== Exemple 4: Supprimer un utilisateur ===');
    await apiClient.deleteData('/users/1');
    print('Utilisateur supprimé avec succès!');
    
  } catch (e) {
    print('Une erreur s\'est produite: $e');
  }
}

// Exemple d'utilisation dans un widget Flutter
class ExempleWidget {
  final ApiClient _apiClient = ApiClient();
  
  // Méthode pour charger des données dans votre app
  Future<List<Map<String, dynamic>>> chargerUtilisateurs() async {
    try {
      final response = await _apiClient.getData('/users');
      
      // Si la réponse contient une liste d'utilisateurs
      if (response['users'] != null) {
        return List<Map<String, dynamic>>.from(response['users']);
      }
      
      return [];
    } catch (e) {
      print('Erreur lors du chargement des utilisateurs: $e');
      return [];
    }
  }
  
  // Méthode pour sauvegarder un utilisateur
  Future<bool> sauvegarderUtilisateur(String nom, String email) async {
    try {
      final donnees = {
        'nom': nom,
        'email': email,
      };
      
      await _apiClient.sendData('/users', donnees);
      return true; // Succès
    } catch (e) {
      print('Erreur lors de la sauvegarde: $e');
      return false; // Échec
    }
  }
}
