import 'package:dio/dio.dart';

class ApiClient {
  late final Dio _dio;
  
  static const String apiUrl = 'https://api_url_here';
  
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;
  
  ApiClient._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: apiUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  /// Setup interceptors (like axios interceptors)
  void _setupInterceptors() {
    // Request interceptor - runs before every request
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add auth token if available
          final token = _getAuthToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          
          print('🚀 REQUEST: ${options.method} ${options.path}');
          print('📤 Data: ${options.data}');
          
          handler.next(options);
        },
        
        onResponse: (response, handler) {
          print('✅ RESPONSE: ${response.statusCode} ${response.requestOptions.path}');
          print('📥 Data: ${response.data}');
          
          handler.next(response);
        },
        
        onError: (error, handler) {
          print('❌ ERROR: ${error.message}');
          print('🔍 Response: ${error.response?.data}');
          
          // Handle common errors
          if (error.response?.statusCode == 401) {
            // Handle unauthorized - maybe redirect to login
            _handleUnauthorized();
          }
          
          handler.next(error);
        },
      ),
    );
  }
  
  /// Get stored auth token (you'll implement this based on your storage solution)
  String? _getAuthToken() {
    // TODO: Get token from secure storage
    // For now, return null
    return null;
  }
  
  /// Handle unauthorized access
  void _handleUnauthorized() {
    // TODO: Clear stored token and redirect to login
    print('🔐 Unauthorized access - redirecting to login');
  }
  
  /// Set auth token for future requests
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }
  
  /// Clear auth token
  void clearAuthToken() {
    _dio.options.headers.remove('Authorization');
  }
  
  // HTTP Methods - similar to axios methods
  
  /// GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }
  
  /// Handle and transform errors
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return NetworkException('Connection timeout. Please check your internet connection.');
        
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['message'] ?? 'Unknown error occurred';
          return ApiException(statusCode ?? 0, message);
        
        case DioExceptionType.cancel:
          return NetworkException('Request was cancelled');
        
        case DioExceptionType.unknown:
        default:
          return NetworkException('Network error occurred. Please try again.');
      }
    }
    
    return NetworkException('Unexpected error occurred');
  }
}

/// Custom exception for API errors
class ApiException implements Exception {
  final int statusCode;
  final String message;
  
  ApiException(this.statusCode, this.message);
  
  @override
  String toString() => 'ApiException: $statusCode - $message';
}

/// Custom exception for network errors
class NetworkException implements Exception {
  final String message;
  
  NetworkException(this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}
