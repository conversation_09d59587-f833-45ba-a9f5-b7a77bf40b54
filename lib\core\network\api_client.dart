import 'package:dio/dio.dart';

// Une classe simple pour faire des appels à une API
class ApiClient {
  // L'objet Dio qui fait les vraies requêtes HTTP
  final Dio _dio;

  // L'adresse de base de votre API (à changer avec votre vraie URL)
  static const String apiUrl = 'https://api_url_here';

  // Constructeur - se lance quand on crée un ApiClient
  ApiClient() : _dio = Dio() {
    // Configuration de base
    _dio.options.baseUrl = apiUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);

    // Headers par défaut (informations envoyées avec chaque requête)
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  // Méthode pour récupérer des données (GET)
  // Exemple: récupérer une liste d'utilisateurs
  Future<Map<String, dynamic>> getData(String endpoint) async {
    try {
      print('📡 Récupération des données depuis: $endpoint');

      final response = await _dio.get(endpoint);

      print('✅ Données reçues avec succès!');
      return response.data;
    } catch (e) {
      print('❌ Erreur lors de la récupération: $e');
      throw Exception('Impossible de récupérer les données: $e');
    }
  }

  // Méthode pour envoyer des données (POST)
  // Exemple: créer un nouvel utilisateur
  Future<Map<String, dynamic>> sendData(
      String endpoint, Map<String, dynamic> data) async {
    try {
      print('📤 Envoi des données vers: $endpoint');
      print('📦 Données: $data');

      final response = await _dio.post(endpoint, data: data);

      print('✅ Données envoyées avec succès!');
      return response.data;
    } catch (e) {
      print('❌ Erreur lors de l\'envoi: $e');
      throw Exception('Impossible d\'envoyer les données: $e');
    }
  }

  // Méthode pour mettre à jour des données (PUT)
  // Exemple: modifier un utilisateur existant
  Future<Map<String, dynamic>> updateData(
      String endpoint, Map<String, dynamic> data) async {
    try {
      print('🔄 Mise à jour des données: $endpoint');

      final response = await _dio.put(endpoint, data: data);

      print('✅ Données mises à jour avec succès!');
      return response.data;
    } catch (e) {
      print('❌ Erreur lors de la mise à jour: $e');
      throw Exception('Impossible de mettre à jour: $e');
    }
  }

  // Méthode pour supprimer des données (DELETE)
  // Exemple: supprimer un utilisateur
  Future<void> deleteData(String endpoint) async {
    try {
      print('🗑️ Suppression: $endpoint');

      await _dio.delete(endpoint);

      print('✅ Suppression réussie!');
    } catch (e) {
      print('❌ Erreur lors de la suppression: $e');
      throw Exception('Impossible de supprimer: $e');
    }
  }
}
