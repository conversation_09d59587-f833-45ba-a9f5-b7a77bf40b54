import '../core/network/api_client.dart';
import '../models/user.dart';

/// Service class for User-related API operations
/// This is like a repository or service layer in other frameworks
class UserService {
  final ApiClient _apiClient;
  
  // Singleton pattern
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  
  UserService._internal() : _apiClient = ApiClient();
  
  /// Get all users
  /// Returns a list of all users from the API
  Future<List<User>> getAllUsers() async {
    try {
      final response = await _apiClient.get<List<dynamic>>('/users');
      
      // Parse the response data into User objects
      final List<dynamic> usersJson = response.data ?? [];
      return usersJson
          .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw _handleServiceError(e, 'Failed to fetch users');
    }
  }
  
  /// Get a single user by ID
  /// Returns a User object or throws an exception if not found
  Future<User> getUserById(int id) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>('/users/$id');
      
      if (response.data == null) {
        throw UserNotFoundException('User with ID $id not found');
      }
      
      return User.fromJson(response.data!);
    } catch (e) {
      if (e is UserNotFoundException) rethrow;
      throw _handleServiceError(e, 'Failed to fetch user with ID $id');
    }
  }
  
  /// Create a new user
  /// Takes user data and returns the created user with assigned ID
  Future<User> createUser(CreateUserRequest userRequest) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/users',
        data: userRequest.toJson(),
      );
      
      if (response.data == null) {
        throw UserServiceException('Failed to create user - no response data');
      }
      
      return User.fromJson(response.data!);
    } catch (e) {
      throw _handleServiceError(e, 'Failed to create user');
    }
  }
  
  /// Update an existing user
  /// Takes user ID and updated data, returns the updated user
  Future<User> updateUser(int id, CreateUserRequest userRequest) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/users/$id',
        data: userRequest.toJson(),
      );
      
      if (response.data == null) {
        throw UserServiceException('Failed to update user - no response data');
      }
      
      return User.fromJson(response.data!);
    } catch (e) {
      throw _handleServiceError(e, 'Failed to update user with ID $id');
    }
  }
  
  /// Partially update a user (PATCH)
  /// Updates only the provided fields
  Future<User> patchUser(int id, Map<String, dynamic> updates) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/users/$id',
        data: updates,
      );
      
      if (response.data == null) {
        throw UserServiceException('Failed to patch user - no response data');
      }
      
      return User.fromJson(response.data!);
    } catch (e) {
      throw _handleServiceError(e, 'Failed to patch user with ID $id');
    }
  }
  
  /// Delete a user
  /// Returns true if deletion was successful
  Future<bool> deleteUser(int id) async {
    try {
      final response = await _apiClient.delete('/users/$id');
      
      // Most APIs return 200 or 204 for successful deletion
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      throw _handleServiceError(e, 'Failed to delete user with ID $id');
    }
  }
  
  /// Search users by name or email
  /// This is an example of a custom endpoint
  Future<List<User>> searchUsers(String query) async {
    try {
      final response = await _apiClient.get<List<dynamic>>(
        '/users',
        queryParameters: {
          'q': query, // This depends on your API's search implementation
        },
      );
      
      final List<dynamic> usersJson = response.data ?? [];
      final allUsers = usersJson
          .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
          .toList();
      
      // Client-side filtering (since JSONPlaceholder doesn't support search)
      return allUsers.where((user) {
        return user.name.toLowerCase().contains(query.toLowerCase()) ||
               user.email.toLowerCase().contains(query.toLowerCase());
      }).toList();
    } catch (e) {
      throw _handleServiceError(e, 'Failed to search users');
    }
  }
  
  /// Get users with pagination
  /// Returns a subset of users based on page and limit
  Future<UserListResponse> getUsersPaginated({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get<List<dynamic>>(
        '/users',
        queryParameters: {
          '_page': page,
          '_limit': limit,
        },
      );
      
      final List<dynamic> usersJson = response.data ?? [];
      final users = usersJson
          .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
          .toList();
      
      // In a real API, you'd get total count from headers or response
      final totalCount = int.tryParse(
        response.headers.value('x-total-count') ?? '0'
      ) ?? users.length;
      
      return UserListResponse(
        users: users,
        totalCount: totalCount,
        page: page,
        limit: limit,
      );
    } catch (e) {
      throw _handleServiceError(e, 'Failed to fetch paginated users');
    }
  }
  
  /// Handle service-level errors
  Exception _handleServiceError(dynamic error, String message) {
    if (error is ApiException) {
      // Handle specific API errors
      switch (error.statusCode) {
        case 404:
          return UserNotFoundException(message);
        case 400:
          return UserValidationException('Invalid user data: ${error.message}');
        case 409:
          return UserConflictException('User already exists: ${error.message}');
        default:
          return UserServiceException('$message: ${error.message}');
      }
    } else if (error is NetworkException) {
      return UserServiceException('Network error: ${error.message}');
    } else {
      return UserServiceException('$message: ${error.toString()}');
    }
  }
}

/// Response model for paginated user lists
class UserListResponse {
  final List<User> users;
  final int totalCount;
  final int page;
  final int limit;
  
  const UserListResponse({
    required this.users,
    required this.totalCount,
    required this.page,
    required this.limit,
  });
  
  bool get hasNextPage => (page * limit) < totalCount;
  bool get hasPreviousPage => page > 1;
  int get totalPages => (totalCount / limit).ceil();
}

/// Custom exceptions for user service
class UserServiceException implements Exception {
  final String message;
  UserServiceException(this.message);
  
  @override
  String toString() => 'UserServiceException: $message';
}

class UserNotFoundException extends UserServiceException {
  UserNotFoundException(String message) : super(message);
}

class UserValidationException extends UserServiceException {
  UserValidationException(String message) : super(message);
}

class UserConflictException extends UserServiceException {
  UserConflictException(String message) : super(message);
}
