/// User data model
/// This represents a user object in your application
class User {
  final int? id;
  final String name;
  final String email;
  final String? phone;
  final String? website;
  final Address? address;
  final Company? company;
  
  const User({
    this.id,
    required this.name,
    required this.email,
    this.phone,
    this.website,
    this.address,
    this.company,
  });
  
  /// Create User from JSON (like parsing API response)
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int?,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      website: json['website'] as String?,
      address: json['address'] != null 
          ? Address.fromJson(json['address'] as Map<String, dynamic>)
          : null,
      company: json['company'] != null
          ? Company.fromJson(json['company'] as Map<String, dynamic>)
          : null,
    );
  }
  
  /// Convert User to JSON (for sending to API)
  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'email': email,
      if (phone != null) 'phone': phone,
      if (website != null) 'website': website,
      if (address != null) 'address': address!.toJson(),
      if (company != null) 'company': company!.toJson(),
    };
  }
  
  /// Create a copy of User with some fields changed
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? website,
    Address? address,
    Company? company,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      website: website ?? this.website,
      address: address ?? this.address,
      company: company ?? this.company,
    );
  }
  
  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}

/// Address data model
class Address {
  final String street;
  final String suite;
  final String city;
  final String zipcode;
  final Geo? geo;
  
  const Address({
    required this.street,
    required this.suite,
    required this.city,
    required this.zipcode,
    this.geo,
  });
  
  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'] as String,
      suite: json['suite'] as String,
      city: json['city'] as String,
      zipcode: json['zipcode'] as String,
      geo: json['geo'] != null
          ? Geo.fromJson(json['geo'] as Map<String, dynamic>)
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'suite': suite,
      'city': city,
      'zipcode': zipcode,
      if (geo != null) 'geo': geo!.toJson(),
    };
  }
}

/// Geographic coordinates
class Geo {
  final String lat;
  final String lng;
  
  const Geo({
    required this.lat,
    required this.lng,
  });
  
  factory Geo.fromJson(Map<String, dynamic> json) {
    return Geo(
      lat: json['lat'] as String,
      lng: json['lng'] as String,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'lat': lat,
      'lng': lng,
    };
  }
}

/// Company data model
class Company {
  final String name;
  final String catchPhrase;
  final String bs;
  
  const Company({
    required this.name,
    required this.catchPhrase,
    required this.bs,
  });
  
  factory Company.fromJson(Map<String, dynamic> json) {
    return Company(
      name: json['name'] as String,
      catchPhrase: json['catchPhrase'] as String,
      bs: json['bs'] as String,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'catchPhrase': catchPhrase,
      'bs': bs,
    };
  }
}

/// Request model for creating/updating users
class CreateUserRequest {
  final String name;
  final String email;
  final String? phone;
  final String? website;
  
  const CreateUserRequest({
    required this.name,
    required this.email,
    this.phone,
    this.website,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      if (phone != null) 'phone': phone,
      if (website != null) 'website': website,
    };
  }
}
